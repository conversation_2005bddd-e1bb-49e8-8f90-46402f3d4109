---
layout: "base.njk"
---

<!-- SECTION 1: Fullscreen Header with Typewriter Subheading -->
<header
  class="h-screen flex flex-col justify-center items-center overflow-hidden"
>
  <h1 class="text-8xl max-md:text-6xl font-extrabold mb-4">{{ projects.title }}</h1>
  <h2 id="subtitle" class="text-2xl font-medium"></h2>
</header>

<!-- SECTION 2: Three-column flex box -->
<section
  class="overflow-hidden justify-center max-md:flex-col flex flex-row items-center p-8 max-md:p-4"
>
  <!-- Column 1: Full-size image -->

  <div
    class="flex-1 border border-black max-md:text-center max-md:p-4 p-12 flex flex-col justify-center min-h-[50vh] m-8"
  >
    <h3 id="highlight-effect-projects" class="text-3xl font-bold">
      <span class="highlight" data-index="0">
        {{ projects.box.title }}

    </span>
    </h3>
    <div class="flex max-md:flex-col flex-row gap-4 items-center">
      <p class="mt-4 text-base text-justify text-gray-600 leading-relaxed">
        {{ projects.box.description }}
      </p>
      <div
        class="flex-1 flex justify-center items-center h-12 border  border-black p-4"
      >
        <a  href={{"/about"  | locale_url }}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20.91"
            height="23.897"
            viewBox="0 0 20.91 23.897"
          >
            <g
              id="Polygon_2"
              data-name="Polygon 2"
              transform="translate(20.91) rotate(90)"
              fill="#000"
            >
              <path
                d="M 23.39700698852539 20.41000175476074 L 0.5000002980232239 20.41000175476074 L 11.94850349426269 0.7071542739868164 L 23.39700698852539 20.41000175476074 Z"
                stroke="none"
              />
              <path
                d="M 11.94850349426269 1.414291381835938 L 1.000000238418579 19.91000175476074 L 22.89700698852539 19.91000175476074 L 11.94850349426269 1.414291381835938 M 11.94850349426269 -9.5367431640625e-07 L 23.89700698852539 20.91000175476074 L 0 20.91000175476074 L 11.94850349426269 -9.5367431640625e-07 Z"
                stroke="none"
                fill="#000"
              />
            </g>
          </svg>
        </a>
      </div>
    </div>
  </div>

  <div class="flex-1">
    <img
      src="/Assets/Images/التلعيب.png"
      alt="Project Image"
      class="object-contain w-full h-[100vh]"
    />
  </div>
  <!-- Column 3: Arrow link -->
</section>
<!-- SECTION 3: Project Grid Section -->
<section class="py-20 px-8">
  <div class="max-w-6xl mx-auto">
    <!-- Search and Filter Row -->
    <div
      class="flex flex-col sm:flex-row justify-center items-center mb-12 gap-4"
    >
      <div
        class="flex w-full sm:w-auto border border-gray-300 rounded px-4 py-2"
      >
        <input
          id="search-input"
          type="text"
          placeholder="{{ projects.search }}"
          class="w-full focus:outline-none bg-transparent"
        />
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-gray-400 ml-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
      <div class="relative">
        <button id="filter-button" class="border border-gray-300 px-4 py-2 text-sm">
          {{ projects.filter }}
        </button>
        <div id="filter-dropdown" class="absolute z-10 mt-1 w-48 bg-white border border-gray-300 rounded shadow-lg hidden">
          <div class="p-2">
            <a href="#" data-filter="all" class="block px-4 py-2 hover:bg-gray-100 bg-black text-white">{{ ideation.allCategories }}</a>
            <a href="#" data-filter="climate" class="block px-4 py-2 hover:bg-gray-100">{{ projects.categories.climate }}</a>
            <a href="#" data-filter="sustainability" class="block px-4 py-2 hover:bg-gray-100">{{ projects.categories.sustainability }}</a>
            <a href="#" data-filter="innovation" class="block px-4 py-2 hover:bg-gray-100">{{ ideation.innovation }}</a>
          </div>
        </div>
      </div>

      <!-- Date Range Filter -->
      <div class="flex items-center gap-2">
        <div class="border border-gray-300 px-2 py-2 rounded">
          <label for="date-range-start" class="text-sm text-gray-600">{{ ideation.dateFrom }}</label>
          <input type="date" id="date-range-start" class="focus:outline-none bg-transparent ml-1">
        </div>
        <div class="border border-gray-300 px-2 py-2 rounded">
          <label for="date-range-end" class="text-sm text-gray-600">{{ ideation.dateTo }}</label>
          <input type="date" id="date-range-end" class="focus:outline-none bg-transparent ml-1">
        </div>
      </div>
    </div>

    <!-- Grid of Projects -->
    <div id="content-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
      {% for project in collections["project"] %}
      {% if project.data.lang == page.lang %}
      <div class="content-item">
        <a href="{{ project.url }}" class="hover:scale-105 transition-transform duration-500 block">
          <div
            class="border border-black text-center border-b-8 border-b-[#043E4B]"
          >
            <img
              src="{{ project.data.featured_image }}"
              alt="{{ project.data.title }}"
              class="w-full h-56 object-cover"
            />
            <div class="p-4 flex flex-col">
              <p
                class="item-tag text-xs text-white p-2 w-auto uppercase bg-[#043E4B] mb-1"
              >
                {{ project.data.company }}
              </p>
              <h4 class="item-title text-sm font-bold mb-2 uppercase m-2">
                {{ project.data.title }}
              </h4>
              <hr class="" />
              <p class="item-summary text-sm text-gray-600 p-3">
                {{ project.data.summary | truncate(100) }}
              </p>
              <div class="flex justify-between items-center mt-2">
                <span class="text-xs text-gray-500">{{ project.data.author }}</span>
                <span class="text-xs text-gray-500 item-date" data-date="{{ project.data.date }}">{{ project.data.date | postDate }}</span>
              </div>
            </div>
          </div>
        </a>
      </div>
      {% endif %}
      {% endfor %}
    </div>

    <!-- Empty state when no results found -->
    <div id="empty-state" class="hidden py-12 text-center">
      <p class="text-xl text-gray-500">{{ ideation.noResults }}</p>
    </div>

    <!-- View More Button -->
    <div class="mt-12 flex justify-center">
      <button
        id="view-more-button"
        class="px-6 py-3 border border-gray-300 text-sm uppercase tracking-wide"
      >
        {{ projects.viewMore }}
      </button>
    </div>
  </div>
</section>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    // ===== Typewriter on subtitle =====
    const messages = [
      "{{ projects.subtitle['1'] }}",
      "{{ projects.subtitle['2'] }}",
      "{{ projects.subtitle['3'] }}"
    ];
    const speed = 100;
    const pause = 2000;
    let idx = 0,
      ch = 0,
      forward = true;
    const sub = document.getElementById("subtitle");
    function typeSub() {
      const msg = messages[idx];
      if (forward) {
        sub.textContent = msg.slice(0, ++ch);
        if (ch === msg.length) {
          forward = false;
          setTimeout(typeSub, pause);
        } else setTimeout(typeSub, speed);
      } else {
        sub.textContent = msg.slice(0, --ch);
        if (ch === 0) {
          forward = true;
          idx = (idx + 1) % messages.length;
          setTimeout(typeSub, speed);
        } else setTimeout(typeSub, speed / 2);
      }
    }
    typeSub();
  });
</script>
