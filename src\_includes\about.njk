---
title: About
layout: "base.njk"
---

<!-- SECTION 1: Fullscreen Header with Typewriter Subheading -->
<header
  class="h-screen flex flex-col justify-center items-center relative overflow-hidden"
>
  <div
    class="absolute inset-0 bg-[url(/Assets/Images/3232321.png)] bg-contain bg-center bg-no-repeat opacity-10"
  ></div>
  <img
    src="/Assets/Images/Logo.png"
    alt="{{site.title }}"
    width="300"
    class="relative z-10"
  />
</header>

<!-- SECTION 2: Quote -->
<section
  class="h-[80vh] w-[90vw] flex max-md:flex-col gap-8 p-8 justify-center mx-auto items-center mb-16"
>
  <p class="text-lg text-gray-600 max-w-2xl text-justify max-md:text-center min-md:p-8" id="highlight-effect">
    <span
      class="highlight text-xl p-3 leading-[2.3em]  text-black font-bold"
    >
      "{{ about.quote }}"
    </span>
  </p>
  <div id="canvas_container" class="w-full h-full relative overflow-hidden">
    {# <!-- Fallback image for mobile devices -->
    <img
      src="/Assets/Images/canvas-mobile.jpg"
      alt="3D Canvas Fallback"
      class="absolute top-0 left-0 w-full h-full object-cover block sm:hidden"
    >
    <!-- Canvas will be inserted here by JavaScript --> #}
  </div>
</section>


<!-- Who We Are Section -->
<section
  class="py-10  md:py-20 mt-10 max-md:text-center md:mt-20 w-[90vw] gap-6 mx-auto flex justify-center items-center border border-black  min-h-[50vh] md:h-[80vh]"
>
  <div
    class="max-w-5xl mx-auto px-4 flex flex-col md:flex-row justify-center items-center gap-6 md:gap-11"
  >
    <img
      class="mx-auto w-[250px] md:w-[350px]"
      src="/Assets/Images/Logo.png"
      alt="Ideation Lab"
    />

    <div class="">
      <h2
        id="highlight-effect-who"
        class="text-4xl font-bold text-gray-900 mb-10 highlight-effect"
      >
        <span class="highlight uppercase">{{ about.who.title }}</span>
      </h2>
      <p class="text-lg text-gray-600 text-justify mb-8 leading-relaxed">
        {{ about.who.description }}
      </p>
      <p class="text-lg text-gray-600 text-justify  leading-relaxed">
        {{ about.who.description2 }}
      </p>
    </div>
  </div>
</section>

<!-- Frost Feather Section -->
<section
  class="py-10 md:py-20 max-md:text-center mt-10 md:mt-20 w-[90vw] gap-6 mx-auto flex justify-center items-center border border-black  min-h-[50vh] md:h-[80vh]"
>
  <div
    class="max-w-5xl mx-auto px-4 flex flex-col md:flex-row justify-center items-center gap-6 md:gap-11"
  >
    <div class="">
      <h2
        id="highlight-effect-mission"
        class="text-4xl font-bold mb-8 highlight-effect"
      >
        <span class="highlight uppercase">{{ about.mission.title }}</span>
      </h2>
      <p class="text-lg text-gray-600 text-justify mb-8 leading-relaxed">
        {{ about.mission.description }}
      </p>
    </div>
    <img
      class="mx-auto w-[250px] md:w-[350px]"
      src="/Assets/Images/3232321.png"
      alt="Frost feather"
    />
  </div>
</section>

<!-- Team Cards Section -->
<section>
  <div
    class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 py-9 w-[90vw] mx-auto text-center"
  >
    <!-- Team Cards -->
    {% for member in about.team.members %}
    <div class="team-card ">
      <div class="p-8 flex min-h-[50vh] flex-col justify-center items-center border border-black">
        <h1 class="team-name text-3xl font-bold p-2 w-auto uppercase mb-1">
          <span class="team-name-highlight">{{ member.name }}</span>
        </h1>
        <hr class="" />
        <p class="text-sm text-gray-600 p-3">
          {{ member.bio }}
        </p>
      </div>
    </div>
    {% endfor %}

    {% if not about.team.members or about.team.members.length == 0 %}
    <!-- Placeholder Team Cards -->
    <div class="team-card">
      <div class="p-8 flex flex-col border border-black">
        <h1 class="team-name text-3xl font-bold p-2 w-auto uppercase mb-1">
          <span class="team-name-highlight">{{ about.team.placeholder.name }}</span>
        </h1>
        <hr class="" />
        <p class="text-sm text-gray-600 p-3">
          {{ about.team.placeholder.bio }}
        </p>
      </div>
    </div>

    <div class="team-card">
      <div class="p-8 flex flex-col border border-black">
        <h1 class="team-name text-3xl font-bold p-2 w-auto uppercase mb-1">
          <span class="team-name-highlight">{{ about.team.placeholder.name }}</span>
        </h1>
        <hr class="" />
        <p class="text-sm text-gray-600 p-3">
          {{ about.team.placeholder.bio }}
        </p>
      </div>
    </div>

    <div class="team-card">
      <div class="p-8 flex flex-col border border-black">
        <h1 class="team-name text-3xl font-bold p-2 w-auto uppercase mb-1">
          <span class="team-name-highlight">{{ about.team.placeholder.name }}</span>
        </h1>
        <hr class="" />
        <p class="text-sm text-gray-600 p-3">
          {{ about.team.placeholder.bio }}
        </p>
      </div>
    </div>
    {% endif %}
  </div>
</section>

<!-- Quote Section -->
<section
  class="h-auto min-h-[50vh] w-[80vw] flex flex-col justify-center mx-auto items-center mb-16"
>
  <div
    class="text-xl text-gray-600 text-justify"
    id="highlight-effect-long"
  >
    <p class="mb-4 highlight-container">
      <span class="highlight text-xl p-3 leading-[2.3em] text-justify text-black" data-index="0">
        "{{ about.longQuote1 }}"
      </span>
    </p>
    <p class="mb-4 highlight-container">
      <span class="highlight text-xl p-3 leading-[2.3em] text-justify text-black" data-index="1">
        "{{ about.longQuote2 }}"
      </span>
    </p>
    <p class="mb-4 highlight-container">
      <span class="highlight text-xl p-3 leading-[2.3em] text-justify text-black" data-index="2">
        "{{ about.longQuote3 }}"
      </span>
    </p>
    <p class="mb-4 highlight-container">
      <span class="highlight text-xl p-3 leading-[2.3em] text-justify text-black" data-index="3">
        "{{ about.longQuote4 }}"
      </span>
    </p>
  </div>
</section>

<!-- Image Section -->
<section
  class="h-[70vh] overflow-hidden w-[90vw] flex flex-col justify-center mx-auto items-center mb-16"
>
  <img
    class="object-conatin"
    src="/Assets/Images/pexels-ds-stories-6991802.jpg"
    alt="bg image"
  />
</section>
